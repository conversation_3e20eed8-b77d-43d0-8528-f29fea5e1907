<?php

$config = [
    'dashboard'                     => array('read'),
    'facility'                      => array('create', 'read', 'update'),
    'categories'                    => array('create', 'read', 'update'),
    'near_by_places'                => array('create', 'read', 'update','delete'),
    'customer'                      => array('read', 'update','delete'),
    'verify_customer_form'          => array('read', 'create', 'update', 'delete'),
    'approve_agent_verification'    => array('read', 'update'),
    'property'                      => array('create', 'read', 'update','delete'),
    'city_images'                   => array('read', 'update', 'delete'),
    'project'                       => array('create', 'read', 'update'),
    'report_reason'                 => array('create', 'read', 'update','delete'),
    'user_reports'                  => array('read'),
    'users_inquiries'               => array('read'),
    'chat'                          => array('create', 'read'),
    'slider'                        => array('create', 'read', 'update','delete'),
    'article'                       => array('create', 'read', 'update','delete'),
    'advertisement'                 => array('read', 'update'),
    'package-feature'               => array('create', 'read', 'update','delete'),
    'package'                       => array('create', 'read', 'update','delete'),
    'user_package'                  => array('read'),
    'calculator'                    => array('read'),
    'payment'                       => array('read'),
    'faqs'                          => array('create', 'read', 'update','delete'),
    'users_accounts'                => array('create', 'read', 'update'),
    'about_us'                      => array('read','update'),
    'privacy_policy'                => array('read', 'update'),
    'terms_conditions'              => array('read', 'update'),
    'language'                      => array('create', 'read', 'update','delete'),
    'system_settings'               => array('read', 'update'),
    'app_settings'                  => array('read', 'update'),
    'web_settings'                  => array('read', 'update'),
    'firebase_settings'             => array('read', 'create'),
    'notification_settings'         => array('read', 'create'),
    'email_configurations'          => array('read', 'create'),
    'email_templates'               => array('read', 'create'),
    'system_update'                 => array('read', 'update'),
    'notification'                  => array('read', 'create','delete'),
    // 'property_inquiry'      => array('read', 'update'),
    // 'type' => array('create', 'read', 'update','delete'),
    // 'unit' => array('create', 'read', 'update','delete'),
];
return $config;
