<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_purchased_packages', function (Blueprint $table) {
            $table->boolean('prop_status')->default(1);
            $table->boolean('adv_status')->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_purchased_packages', function (Blueprint $table) {
            //
        });
    }
};
